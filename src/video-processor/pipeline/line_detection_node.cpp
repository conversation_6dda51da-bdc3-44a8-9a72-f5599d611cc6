#include "./line_detection_node.h"
#include "../signal-processing/sync-by-frwd/sync_by_frwd.h"
#include "../../types.h"
#include "./devtools/data_exporter.hpp"

namespace IQVideoProcessor::Pipeline {

std::vector<ComplexType> frontGraphic;

LineDetectionNode::LineDetectionNode(SampleRateType sampleRate, size_t maxVideoLineSamples)
  : sampleRate_(sampleRate),
    maxVideoLineSamples_(maxVideoLineSamples)
  {
  _ds500kHz_.totalSamples = 0;
  setRunning();
}

LineDetectionNode::~LineDetectionNode() {
  PipelineComponent::stop();
}

bool LineDetectionNode::process(DemodulatedSegment& segment) {
  if (!running()) return false;

  const auto effectiveStartIdx = segment.leftOverlapSamples;
  const auto effectiveSamples = segment.effectiveSamples;

  auto& filteredSegment = getAveFiltered500kHzSegment(segment);
  const auto [minSampleValue, maxSampleValue] = getMinMax(&filteredSegment.data[effectiveStartIdx], effectiveSamples);
  const auto signalScale = maxSampleValue - minSampleValue;

  // SLOPE [EXPERIMENTAL]: 0.15 of the signal range per half of the average size samples
  ComplexType pulseThresholdTrigDelta = signalScale * static_cast<ComplexType>(0.15);
  ComplexType pulseThresholdSize = static_cast<ComplexType>(filteredSegment.aveSize) / static_cast<ComplexType>(2);
  // SYNC VALUE [EXPERIMENTAL]: 0.18 of the signal range
  auto pulseSyncValue = minSampleValue + signalScale * static_cast<ComplexType>(0.18); // 18% of the signal range

  SignalProcessing::SyncByFrwd<ComplexType> syncByFrwd(&filteredSegment.data[0], filteredSegment.effectiveSamples);
  ComplexType position = 0;
  bool found = false;
  bool ok = syncByFrwd(
    true, // to EOF
    -1, // falling front
    pulseSyncValue,
    0.0, // from position
    0.0, // elements (not used)
    pulseThresholdSize, // threshold size
    pulseThresholdTrigDelta,
    position, // output position
    found // output found flag
  );

  if (frontGraphic.size() != segment.totalSamples) frontGraphic.resize(segment.totalSamples);
  std::fill(frontGraphic.begin(), frontGraphic.end(), 0);
  if (found) {
    // If a front was found, mark it in the front graphic
    const auto frontPosition = static_cast<size_t>(position);
    frontGraphic[frontPosition] = pulseSyncValue;
  }
  DevTools::export_debug_data<ComplexType>("LDN", "fronts", segment.segmentIndex, frontGraphic.data(), frontGraphic.size());
  DevTools::export_debug_data<ComplexType>("LDN", "original", segment.segmentIndex, segment.data.data(), segment.totalSamples);
  DevTools::export_debug_data<ComplexType>("LDN", "ave500kHz", filteredSegment.segmentIndex, filteredSegment.data.data(), filteredSegment.totalSamples);

  if (!ok) {
    // If syncByFrwd failed, we cannot process this segment
    return false;
  }

  return running();
}

const AveFilteredDemodulatedSegment& LineDetectionNode::getAveFiltered500kHzSegment(const DemodulatedSegment & segment) {
  if (_ds500kHz_.totalSamples == 0) {
    // Initialize the 500kHz segment with the first segment data
    _ds500kHz_.data.resize(segment.totalSamples);
    _ds500kHz_.totalSamples = segment.totalSamples;
    _ds500kHz_.effectiveSamples = segment.effectiveSamples;
    _ds500kHz_.leftOverlapSamples = segment.leftOverlapSamples;
    // Calculating the filter divider based on the sample rate
    _ds500kHz_.halfAveSize = (sampleRate_ / 500000) >> 1; // 500kHz
    _ds500kHz_.aveSize = (_ds500kHz_.halfAveSize << 1) + 1; // Force to odd: even -> +1, odd unchanged
  }
  _ds500kHz_.segmentIndex = segment.segmentIndex;

  const auto aveSize = _ds500kHz_.aveSize;
  const auto halfAveSize = _ds500kHz_.halfAveSize;

  ComplexType summ = 0;
  for (size_t i = 0; i < aveSize; ++i) {
    summ += segment.data[i];
  }
  // Fill the start of the segment with the initial sum
  for (size_t i = 0; i <= halfAveSize; ++i) {
    _ds500kHz_.data[i] = summ; // Fill the first half with the initial sum
  }

  auto summLeaveIdx = 0;
  auto summEnterIdx = aveSize;
  auto summWriteIdx = halfAveSize + 1;

  // Sliding window to calculate the sum for the rest of the segment
  while (summEnterIdx < segment.totalSamples) {
    summ -= segment.data[summLeaveIdx++];
    summ += segment.data[summEnterIdx++];
    _ds500kHz_.data[summWriteIdx++] = summ;
  }
  // Fill the rest of the segment with the last sum
  while (summWriteIdx < segment.totalSamples) {
    _ds500kHz_.data[summWriteIdx++] = summ;
  }

  return _ds500kHz_;
}

std::tuple<ComplexType, ComplexType> LineDetectionNode::getMinMax(const ComplexType* data, const size_t elements) {
  ComplexType minVal = data[0];
  ComplexType maxVal = data[0];
  for (size_t i = 1; i < elements; ++i) {
    if (data[i] < minVal) minVal = data[i];
    if (data[i] > maxVal) maxVal = data[i];
  }
  return std::make_tuple(minVal, maxVal);
}

} // namespace IQVideoProcessor::Pipeline
